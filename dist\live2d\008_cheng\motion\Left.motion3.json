{"Version": 3, "Meta": {"Duration": 4.167, "Fps": 30.0, "Loop": true, "AreBeziersRestricted": false, "CurveCount": 17, "TotalSegmentCount": 141, "TotalPointCount": 400, "UserDataCount": 0, "TotalUserDataSize": 0}, "Curves": [{"Target": "Parameter", "Id": "Param4", "Segments": [0, 0, 1, 0.267, 0, 0.533, 0, 0.8, 0, 1, 0.889, 0, 0.978, 0, 1.067, 0, 1, 1.156, 0, 1.244, 0, 1.333, 0, 1, 1.411, 0, 1.489, 0, 1.567, 0, 1, 1.611, 0, 1.656, 0, 1.7, 0, 1, 1.744, 0, 1.789, 0, 1.833, 0, 1, 1.878, 0, 1.922, 0, 1.967, 0, 1, 2.078, 0, 2.189, 0, 2.3, 0, 1, 2.344, 0, 2.389, -0.1, 2.433, -0.1, 1, 2.622, -0.1, 2.811, -0.1, 3, -0.1, 1, 3.044, -0.1, 3.089, 0, 3.133, 0, 1, 3.256, 0, 3.378, 0, 3.5, 0, 0, 4.167, 0]}, {"Target": "Parameter", "Id": "Param", "Segments": [0, 0, 1, 0.267, 0, 0.533, 0, 0.8, 0, 1, 0.889, 0, 0.978, 0, 1.067, 0, 1, 1.156, 0, 1.244, 0, 1.333, 0, 1, 1.411, 0, 1.489, 0, 1.567, 0, 1, 1.611, 0, 1.656, 0, 1.7, 0, 1, 1.744, 0, 1.789, 0, 1.833, 0, 1, 1.878, 0, 1.922, 0, 1.967, 0, 1, 2.078, 0, 2.189, 0, 2.3, 0, 1, 2.344, 0, 2.389, -0.1, 2.433, -0.1, 1, 2.622, -0.1, 2.811, -0.1, 3, -0.1, 1, 3.044, -0.1, 3.089, 0, 3.133, 0, 1, 3.256, 0, 3.378, 0, 3.5, 0, 0, 4.167, 0]}, {"Target": "Parameter", "Id": "Param3", "Segments": [0, 0, 1, 0.267, 0, 0.533, 0, 0.8, 0, 1, 0.889, 0, 0.978, 0, 1.067, 0, 1, 1.156, 0, 1.244, 0, 1.333, 0, 1, 1.411, 0, 1.489, 0, 1.567, 0, 1, 1.611, 0, 1.656, 0, 1.7, 0, 1, 1.744, 0, 1.789, 0, 1.833, 0, 1, 1.878, 0, 1.922, 0, 1.967, 0, 1, 2.078, 0, 2.189, 0, 2.3, 0, 1, 2.344, 0, 2.389, -0.1, 2.433, -0.1, 1, 2.622, -0.1, 2.811, -0.1, 3, -0.1, 1, 3.044, -0.1, 3.089, 0, 3.133, 0, 1, 3.256, 0, 3.378, 0, 3.5, 0, 0, 4.167, 0]}, {"Target": "Parameter", "Id": "Param2", "Segments": [0, 0, 1, 0.267, 0, 0.533, 0, 0.8, 0, 1, 0.889, 0, 0.978, 0, 1.067, 0, 1, 1.156, 0, 1.244, 0, 1.333, 0, 1, 1.411, 0, 1.489, 0, 1.567, 0, 1, 1.611, 0, 1.656, 0, 1.7, 0, 1, 1.744, 0, 1.789, 0, 1.833, 0, 1, 1.878, 0, 1.922, 0, 1.967, 0, 1, 2.078, 0, 2.189, 0, 2.3, 0, 1, 2.344, 0, 2.389, -0.1, 2.433, -0.1, 1, 2.622, -0.1, 2.811, -0.1, 3, -0.1, 1, 3.044, -0.1, 3.089, 0, 3.133, 0, 0, 4.167, 0]}, {"Target": "Parameter", "Id": "Param6", "Segments": [0, 0, 1, 0.267, 0, 0.533, 0, 0.8, 0, 1, 0.889, 0, 0.978, 0, 1.067, 0, 1, 1.156, 0, 1.244, 0.5, 1.333, 0.5, 1, 1.411, 0.5, 1.489, 0, 1.567, 0, 1, 1.656, 0, 1.744, 0, 1.833, 0, 1, 1.989, 0, 2.144, 0, 2.3, 0, 1, 2.578, 0, 2.856, 0, 3.133, 0, 0, 4.167, 0]}, {"Target": "Parameter", "Id": "Param5", "Segments": [0, 0, 1, 0.267, 0, 0.533, 0, 0.8, 0, 1, 0.889, 0, 0.978, 0, 1.067, 0, 1, 1.156, 0, 1.244, 0.6, 1.333, 0.6, 1, 1.411, 0.6, 1.489, 0, 1.567, 0, 1, 1.611, 0, 1.656, 0, 1.7, 0, 1, 1.944, 0, 2.189, 0, 2.433, 0, 0, 4.167, 0]}, {"Target": "Parameter", "Id": "Param7", "Segments": [0, 0, 0, 1.567, 0, 1, 1.611, 0, 1.656, 0, 1.7, 0, 1, 1.744, 0, 1.789, 0, 1.833, 0, 1, 1.989, 0, 2.144, 0, 2.3, 0, 1, 2.344, 0, 2.389, 0, 2.433, 0, 0, 4.167, 0]}, {"Target": "Parameter", "Id": "Param9", "Segments": [0, 0, 0, 1.567, 0, 1, 1.656, 0, 1.744, 0, 1.833, 0, 1, 2.033, 0, 2.233, 0, 2.433, 0, 1, 2.667, 0, 2.9, 0, 3.133, 0, 1, 3.256, 0, 3.378, 0, 3.5, 0, 0, 4.167, 0]}, {"Target": "Parameter", "Id": "Param8", "Segments": [0, 0, 1, 0.044, 0, 0.089, 0.2, 0.133, 0.2, 1, 0.356, 0.2, 0.578, 0.2, 0.8, 0.2, 1, 0.889, 0.2, 0.978, 0.2, 1.067, 0.2, 1, 1.156, 0.2, 1.244, 0.2, 1.333, 0.2, 1, 1.411, 0.2, 1.489, 0.2, 1.567, 0.2, 1, 1.611, 0.2, 1.656, 0.2, 1.7, 0.2, 1, 1.744, 0.2, 1.789, 0.2, 1.833, 0.2, 1, 1.878, 0.2, 1.922, 0.2, 1.967, 0.2, 1, 2.078, 0.2, 2.189, 0.2, 2.3, 0.2, 1, 2.344, 0.2, 2.389, 0.2, 2.433, 0.2, 1, 2.622, 0.2, 2.811, 0.2, 3, 0.2, 1, 3.044, 0.2, 3.089, 0.2, 3.133, 0.2, 1, 3.256, 0.2, 3.378, 0, 3.5, 0, 0, 4.167, 0]}, {"Target": "Parameter", "Id": "Param10", "Segments": [0, 0, 1, 0.044, 0, 0.089, -0.1, 0.133, -0.1, 1, 0.356, -0.1, 0.578, -0.1, 0.8, -0.1, 1, 0.889, -0.1, 0.978, -0.1, 1.067, -0.1, 1, 1.156, -0.1, 1.244, -0.1, 1.333, -0.1, 1, 1.411, -0.1, 1.489, -0.1, 1.567, -0.1, 1, 1.611, -0.1, 1.656, -0.1, 1.7, -0.1, 1, 1.744, -0.1, 1.789, -0.1, 1.833, -0.1, 1, 1.878, -0.1, 1.922, -0.1, 1.967, -0.1, 1, 2.078, -0.1, 2.189, -0.1, 2.3, -0.1, 1, 2.344, -0.1, 2.389, -0.1, 2.433, -0.1, 1, 2.622, -0.1, 2.811, -0.1, 3, -0.1, 1, 3.044, -0.1, 3.089, -0.1, 3.133, -0.1, 1, 3.256, -0.1, 3.378, 0, 3.5, 0, 0, 4.167, 0]}, {"Target": "Parameter", "Id": "Param12", "Segments": [0, 0, 1, 0.522, 0, 1.044, 0, 1.567, 0, 1, 1.611, 0, 1.656, 0, 1.7, 0, 1, 1.744, 0, 1.789, 1, 1.833, 1, 1, 1.878, 1, 1.922, 0, 1.967, 0, 1, 2.122, 0, 2.278, 0, 2.433, 0, 1, 2.622, 0, 2.811, 0, 3, 0, 1, 3.044, 0, 3.089, 0, 3.133, 0, 0, 4.167, 0]}, {"Target": "Parameter", "Id": "Param13", "Segments": [0, 0, 1, 0.267, 0, 0.533, 0, 0.8, 0, 1, 0.978, 0, 1.156, 0, 1.333, 0, 1, 1.411, 0, 1.489, 0, 1.567, 0, 1, 1.611, 0, 1.656, 0, 1.7, 0, 1, 1.744, 0, 1.789, 0, 1.833, 0, 1, 1.878, 0, 1.922, 0, 1.967, 0, 1, 2.122, 0, 2.278, 0, 2.433, 0, 0, 4.167, 0]}, {"Target": "Parameter", "Id": "Param11", "Segments": [0, 0, 0, 1.333, 0, 1, 1.411, 0, 1.489, 0, 1.567, 0, 1, 1.611, 0, 1.656, 0, 1.7, 0, 1, 1.744, 0, 1.789, 1, 1.833, 1, 1, 1.878, 1, 1.922, 0, 1.967, 0, 1, 2.122, 0, 2.278, 0, 2.433, 0, 0, 4.167, 0]}, {"Target": "Parameter", "Id": "Param15", "Segments": [0, 0, 1, 0.267, 0, 0.533, 0, 0.8, 0, 1, 0.978, 0, 1.156, 0, 1.333, 0, 1, 1.411, 0, 1.489, 0, 1.567, 0, 1, 1.611, 0, 1.656, 0, 1.7, 0, 1, 1.744, 0, 1.789, 0, 1.833, 0, 1, 1.878, 0, 1.922, 0, 1.967, 0, 0, 4.167, 0]}, {"Target": "Parameter", "Id": "Param14", "Segments": [0, 0, 0, 4.167, 0]}, {"Target": "Parameter", "Id": "Param16", "Segments": [0, 0, 0, 4.167, 0]}, {"Target": "Parameter", "Id": "Param21", "Segments": [0, 0, 1, 0.044, 0, 0.089, 0, 0.133, 0, 1, 0.311, 0, 0.489, 0, 0.667, 0, 0, 4.167, 0]}]}