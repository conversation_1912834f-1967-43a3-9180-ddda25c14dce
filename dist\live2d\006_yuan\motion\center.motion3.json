{"Version": 3, "Meta": {"Duration": 6.233, "Fps": 30.0, "Loop": true, "AreBeziersRestricted": true, "CurveCount": 16, "TotalSegmentCount": 134, "TotalPointCount": 370, "UserDataCount": 0, "TotalUserDataSize": 0}, "Curves": [{"Target": "Parameter", "Id": "Param3", "Segments": [0, 0, 0, 1.533, 0, 1, 1.622, 0, 1.711, -0.05, 1.8, -0.05, 1, 1.933, -0.05, 2.067, -0.05, 2.2, -0.05, 1, 2.267, -0.05, 2.333, -0.05, 2.4, -0.05, 1, 2.922, -0.05, 3.444, 0, 3.967, 0, 0, 6.233, 0]}, {"Target": "Parameter", "Id": "Param5", "Segments": [0, 0, 1, 0.111, 0, 0.222, 0, 0.333, 0, 1, 0.433, 0, 0.533, 0, 0.633, 0, 1, 0.644, 0, 0.656, 0.1, 0.667, 0.1, 1, 0.756, 0.1, 0.844, 0.1, 0.933, 0.1, 1, 1.044, 0.1, 1.156, 0.1, 1.267, 0.1, 1, 1.356, 0.1, 1.444, 0.1, 1.533, 0.1, 1, 1.622, 0.1, 1.711, -0.1, 1.8, -0.1, 1, 1.933, -0.1, 2.067, -0.1, 2.2, -0.1, 1, 2.267, -0.1, 2.333, -0.1, 2.4, -0.1, 1, 2.922, -0.1, 3.444, 0, 3.967, 0, 0, 6.233, 0]}, {"Target": "Parameter", "Id": "Param4", "Segments": [0, 0, 0, 1.533, 0, 1, 1.622, 0, 1.711, -0.05, 1.8, -0.05, 1, 1.933, -0.05, 2.067, -0.05, 2.2, -0.05, 1, 2.267, -0.05, 2.333, -0.05, 2.4, -0.05, 1, 2.922, -0.05, 3.444, 0, 3.967, 0, 0, 6.233, 0]}, {"Target": "Parameter", "Id": "Param6", "Segments": [0, 0, 1, 0.111, 0, 0.222, 0, 0.333, 0, 1, 0.433, 0, 0.533, 0, 0.633, 0, 1, 0.644, 0, 0.656, -0.11, 0.667, -0.11, 1, 0.756, -0.11, 0.844, -0.11, 0.933, -0.11, 1, 1.044, -0.11, 1.156, -0.11, 1.267, -0.11, 1, 1.356, -0.11, 1.444, -0.11, 1.533, -0.11, 1, 1.622, -0.11, 1.711, 0.1, 1.8, 0.1, 1, 1.933, 0.1, 2.067, 0.1, 2.2, 0.1, 1, 2.267, 0.1, 2.333, 0.1, 2.4, 0.1, 1, 2.922, 0.1, 3.444, 0, 3.967, 0, 0, 6.233, 0]}, {"Target": "Parameter", "Id": "Param14", "Segments": [0, 0, 1, 0.111, 0, 0.222, 0, 0.333, 0, 1, 0.533, 0, 0.733, 0, 0.933, 0, 1, 1.044, 0, 1.156, 0, 1.267, 0, 1, 1.444, 0, 1.622, 0, 1.8, 0, 1, 1.933, 0, 2.067, 0, 2.2, 0, 1, 2.267, 0, 2.333, 0, 2.4, 0, 1, 2.411, 0, 2.422, 1, 2.433, 1, 1, 2.933, 1, 3.433, -1, 3.933, -1, 1, 3.944, -1, 3.956, 0, 3.967, 0, 0, 6.233, 0]}, {"Target": "Parameter", "Id": "Param16", "Segments": [0, 0, 1, 0.111, 0, 0.222, 0, 0.333, 0, 1, 0.533, 0, 0.733, 0, 0.933, 0, 1, 1.044, 0, 1.156, 0, 1.267, 0, 1, 1.444, 0, 1.622, 0, 1.8, 0, 1, 1.933, 0, 2.067, 0, 2.2, 0, 1, 2.267, 0, 2.333, 0, 2.4, 0, 1, 2.411, 0, 2.422, 1, 2.433, 1, 1, 2.933, 1, 3.433, 1, 3.933, 1, 1, 3.944, 1, 3.956, 0, 3.967, 0, 0, 6.233, 0]}, {"Target": "Parameter", "Id": "Param17", "Segments": [0, 0, 1, 0.111, 0, 0.222, 0, 0.333, 0, 1, 0.533, 0, 0.733, 0, 0.933, 0, 1, 1.044, 0, 1.156, 0, 1.267, 0, 1, 1.444, 0, 1.622, 0, 1.8, 0, 1, 1.933, 0, 2.067, 0, 2.2, 0, 1, 2.267, 0, 2.333, 0, 2.4, 0, 1, 2.411, 0, 2.422, 1, 2.433, 1, 1, 2.933, 1, 3.433, 1, 3.933, 1, 1, 3.944, 1, 3.956, 0, 3.967, 0, 0, 6.233, 0]}, {"Target": "Parameter", "Id": "Param7", "Segments": [0, 0, 1, 0.111, 0, 0.222, 0, 0.333, 0, 1, 0.444, 0, 0.556, 1, 0.667, 1, 1, 0.756, 1, 0.844, 0, 0.933, 0, 1, 1.044, 0, 1.156, 0, 1.267, 0, 1, 1.356, 0, 1.444, 1, 1.533, 1, 1, 1.622, 1, 1.711, 0, 1.8, 0, 1, 1.933, 0, 2.067, 0, 2.2, 0, 1, 2.267, 0, 2.333, 1, 2.4, 1, 1, 2.922, 1, 3.444, 0.982, 3.967, 0.9, 1, 4.089, 0.881, 4.211, -0.9, 4.333, -0.9, 1, 4.433, -0.9, 4.533, 0, 4.633, 0, 0, 6.233, 0]}, {"Target": "Parameter", "Id": "Param8", "Segments": [0, 0, 1, 0.111, 0, 0.222, 0, 0.333, 0, 1, 0.444, 0, 0.556, 1, 0.667, 1, 1, 0.756, 1, 0.844, 0, 0.933, 0, 1, 1.044, 0, 1.156, 0, 1.267, 0, 1, 1.356, 0, 1.444, 1, 1.533, 1, 1, 1.622, 1, 1.711, 0, 1.8, 0, 1, 1.933, 0, 2.067, 0, 2.2, 0, 1, 2.267, 0, 2.333, 1, 2.4, 1, 1, 2.922, 1, 3.444, 0.982, 3.967, 0.9, 1, 4.089, 0.881, 4.211, -0.9, 4.333, -0.9, 1, 4.433, -0.9, 4.533, 0, 4.633, 0, 0, 6.233, 0]}, {"Target": "Parameter", "Id": "Param9", "Segments": [0, 0, 0, 3.967, 0, 1, 4.089, 0, 4.211, 0.1, 4.333, 0.1, 1, 4.433, 0.1, 4.533, 0, 4.633, 0, 0, 6.233, 0]}, {"Target": "Parameter", "Id": "Param15", "Segments": [0, 0, 0, 3.967, 0, 1, 4.089, 0, 4.211, 0.1, 4.333, 0.1, 1, 4.433, 0.1, 4.533, 0, 4.633, 0, 0, 6.233, 0]}, {"Target": "Parameter", "Id": "Param22", "Segments": [0, 0, 1, 0.811, 0, 1.622, -1, 2.433, -1, 1, 2.444, -1, 2.456, 1, 2.467, 1, 1, 2.544, 1, 2.622, 1, 2.7, 1, 1, 3.111, 1, 3.522, 1, 3.933, 1, 1, 3.944, 1, 3.956, -1, 3.967, -1, 0, 6.233, -1]}, {"Target": "Parameter", "Id": "Param18", "Segments": [0, 0.9, 0, 2.467, 0.9, 1, 2.544, 0.9, 2.622, -0.5, 2.7, -0.5, 1, 2.778, -0.5, 2.856, 0.2, 2.933, 0.2, 1, 3.011, 0.2, 3.089, -0.5, 3.167, -0.5, 1, 3.256, -0.5, 3.344, 0.2, 3.433, 0.2, 1, 3.522, 0.2, 3.611, -0.5, 3.7, -0.5, 1, 3.778, -0.5, 3.856, 1, 3.933, 1, 0, 6.233, 1]}, {"Target": "Parameter", "Id": "Param19", "Segments": [0, 0.9, 0, 2.467, 0.9, 1, 2.544, 0.9, 2.622, -0.5, 2.7, -0.5, 1, 2.778, -0.5, 2.856, 0.2, 2.933, 0.2, 1, 3.011, 0.2, 3.089, -0.5, 3.167, -0.5, 1, 3.256, -0.5, 3.344, 0.2, 3.433, 0.2, 1, 3.522, 0.2, 3.611, -0.5, 3.7, -0.5, 1, 3.778, -0.5, 3.856, 1, 3.933, 1, 0, 6.233, 1]}, {"Target": "Parameter", "Id": "Param20", "Segments": [0, 0, 0, 2.467, 0, 1, 2.544, 0, 2.622, -0.2, 2.7, -0.2, 1, 2.778, -0.2, 2.856, 0, 2.933, 0, 1, 3.011, 0, 3.089, -0.2, 3.167, -0.2, 1, 3.256, -0.2, 3.344, 0, 3.433, 0, 1, 3.522, 0, 3.611, -0.2, 3.7, -0.2, 1, 3.778, -0.2, 3.856, 0, 3.933, 0, 0, 6.233, 0]}, {"Target": "Parameter", "Id": "Param21", "Segments": [0, 0, 0, 2.467, 0, 1, 2.544, 0, 2.622, -0.2, 2.7, -0.2, 1, 2.778, -0.2, 2.856, 0, 2.933, 0, 1, 3.011, 0, 3.089, -0.2, 3.167, -0.2, 1, 3.256, -0.2, 3.344, 0, 3.433, 0, 1, 3.522, 0, 3.611, -0.2, 3.7, -0.2, 1, 3.778, -0.2, 3.856, 0, 3.933, 0, 0, 6.233, 0]}]}