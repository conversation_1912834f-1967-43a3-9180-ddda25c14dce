2025-08-04 09:59:40.627 | INFO     | __main__:setup_static_files:79 - 已注册文件路由: /config.json -> dist\config.json
2025-08-04 09:59:40.634 | INFO     | __main__:setup_static_files:52 - 已挂载静态目录: /css -> dist\css
2025-08-04 09:59:40.635 | INFO     | __main__:setup_static_files:79 - 已注册文件路由: /favicon.ico -> dist\favicon.ico
2025-08-04 09:59:40.636 | INFO     | __main__:setup_static_files:79 - 已注册文件路由: /index.html -> dist\index.html
2025-08-04 09:59:40.636 | INFO     | __main__:setup_static_files:52 - 已挂载静态目录: /js -> dist\js
2025-08-04 09:59:40.637 | INFO     | __main__:setup_static_files:52 - 已挂载静态目录: /live2d -> dist\live2d
2025-08-04 09:59:40.639 | INFO     | __main__:setup_static_files:79 - 已注册文件路由: /live2dcubismcore.min.js -> dist\live2dcubismcore.min.js
2025-08-04 09:59:40.639 | INFO     | __main__:setup_static_files:79 - 已注册文件路由: /README_CONFIG.md -> dist\README_CONFIG.md
2025-08-04 09:59:40.639 | WARNING  | src.robot_client:__init__:44 - ROS2不可用，将使用模拟模式
2025-08-04 09:59:40.641 | INFO     | src.expression_mapper:_load_config:171 - 表情配置已加载: config/expressions.json
2025-08-04 09:59:40.642 | INFO     | src.expression_mapper:_load_config:171 - 表情配置已加载: config/expressions.json
2025-08-04 09:59:40.643 | INFO     | src.expression_mapper:_load_config:171 - 表情配置已加载: config/expressions.json
2025-08-04 09:59:40.643 | INFO     | src.remote_signal_processor:__init__:45 - 表情触发器已初始化
2025-08-04 09:59:40.711 | INFO     | __main__:<module>:746 - Live2D WebSocket服务器线程已启动，端口: 8002
2025-08-04 09:59:40.711 | INFO     | __main__:<module>:758 - 启动集成服务器...
2025-08-04 09:59:40.712 | INFO     | __main__:<module>:759 - - HTTP/API 服务: http://localhost:8000
2025-08-04 09:59:40.712 | INFO     | __main__:<module>:760 - - Live2D 界面: http://localhost:8000/live2d
2025-08-04 09:59:40.712 | INFO     | __main__:<module>:761 - - Live2D WebSocket: ws://localhost:8002
2025-08-04 09:59:40.713 | INFO     | __main__:<module>:762 - - 控制面板: http://localhost:8000
2025-08-04 09:59:40.758 | INFO     | __main__:start_live2d_websocket_server:605 - Live2D WebSocket服务器已启动在 ws://0.0.0.0:8002
2025-08-04 09:59:40.761 | INFO     | src.expression_controller:start:31 - 表情控制器已启动
2025-08-04 09:59:40.762 | INFO     | src.expression_controller:start:31 - 表情控制器已启动
2025-08-04 09:59:40.762 | INFO     | src.message_handler:start:23 - 消息处理器已启动
2025-08-04 09:59:40.762 | INFO     | src.action_queue_manager:start:145 - 动作队列管理器已启动
2025-08-04 09:59:40.764 | INFO     | src.queue_execution_engine:start:92 - 队列执行引擎已启动
2025-08-04 09:59:40.764 | INFO     | src.queue_status_manager:start:56 - 队列状态管理器已启动
2025-08-04 09:59:40.764 | ERROR    | src.remote_controller:start_listening:69 - ROS2不可用，无法启动表情触发监听
2025-08-04 09:59:40.764 | INFO     | src.remote_signal_processor:start:60 - 表情触发器已启动
2025-08-04 09:59:40.764 | WARNING  | src.remote_signal_processor:_start_joy_listener:74 - Joy监听器不可用，跳过启动
2025-08-04 09:59:40.765 | INFO     | src.remote_signal_processor:start:66 - 遥控器信号处理器已启动
2025-08-04 09:59:40.765 | INFO     | src.unified_controller:start:99 - 统一控制器已启动，启用的控制器: ['websocket', 'expression_trigger', 'joy_listener', 'action_queue']
2025-08-04 09:59:40.765 | INFO     | __main__:startup_event:547 - 应用启动完成
2025-08-04 09:59:40.765 | INFO     | src.queue_execution_engine:_scheduler_loop:123 - 队列调度器已启动
2025-08-04 09:59:40.766 | INFO     | src.queue_execution_engine:_monitor_loop:178 - 队列监控器已启动
2025-08-04 09:59:40.766 | INFO     | src.queue_execution_engine:_cleanup_loop:327 - 队列清理器已启动
2025-08-04 09:59:43.726 | INFO     | __main__:browser_automation:620 - SSH环境检测: 否
2025-08-04 09:59:43.727 | INFO     | __main__:browser_automation:621 - 当前环境变量 DISPLAY: 未设置
2025-08-04 09:59:43.727 | INFO     | __main__:browser_automation:630 - 已设置 DISPLAY=:0
2025-08-04 09:59:43.727 | INFO     | __main__:browser_automation:632 - 正在打开浏览器: http://localhost:8000/live2d
2025-08-04 09:59:43.753 | WARNING  | __main__:browser_automation:662 - 未找到 epiphany-browser
2025-08-04 09:59:43.753 | INFO     | __main__:browser_automation:667 - epiphany-browser 启动失败，尝试系统默认浏览器
2025-08-04 09:59:44.114 | INFO     | __main__:browser_automation:671 - 系统默认浏览器启动成功
2025-08-04 09:59:44.114 | INFO     | __main__:browser_automation:683 - 等待浏览器打开...
2025-08-04 09:59:45.462 | INFO     | __main__:handle_live2d_client:571 - 新的Live2D客户端连接 [127.0.0.1:10829] 在8002端口，当前连接数: 1
2025-08-04 09:59:46.118 | INFO     | __main__:browser_automation:692 - 移动鼠标到屏幕中心点 (1536.0, 960.0)
2025-08-04 09:59:46.743 | INFO     | __main__:browser_automation:699 - 按下F11键进入全屏模式
2025-08-04 09:59:47.865 | INFO     | src.mouse_utils:_hide_cursor_windows:71 - 鼠标光标已隐藏 (Windows)
2025-08-04 09:59:47.974 | INFO     | __main__:browser_automation:715 - 已将鼠标移至屏幕边缘
2025-08-04 09:59:55.425 | INFO     | __main__:handle_live2d_client:600 - Live2D客户端 [127.0.0.1:10829] 断开连接，剩余连接数: 0
2025-08-04 09:59:55.873 | INFO     | __main__:handle_live2d_client:571 - 新的Live2D客户端连接 [127.0.0.1:10835] 在8002端口，当前连接数: 1
2025-08-04 10:00:29.754 | INFO     | src.connection_manager:connect:21 - 新连接已建立，当前连接数: 1
2025-08-04 10:00:29.755 | INFO     | __main__:websocket_endpoint:293 - 新的WebSocket连接: Address(host='127.0.0.1', port=10942)
2025-08-04 10:00:31.629 | INFO     | __main__:websocket_endpoint:299 - 收到消息: {"action":"move_head_and_ear","yaw_cmd":"left"}
2025-08-04 10:00:31.629 | WARNING  | src.robot_client:move_head_and_ear_direct:65 - ROS2未初始化，使用模拟模式
2025-08-04 10:00:31.629 | INFO     | src.expression_controller:handle_expression_message:83 - 表情消息处理成功: move_head_and_ear
2025-08-04 10:00:31.630 | INFO     | src.message_handler:handle_message:61 - 消息处理成功: move_head_and_ear
2025-08-04 10:00:31.630 | INFO     | __main__:enhanced_app_broadcast_callback:170 - 已转发Live2D消息: {'type': 'playMotion', 'group': 'left', 'index': 0}
2025-08-04 10:00:31.632 | INFO     | src.unified_controller:handle_websocket_message:158 - 已触发应用层广播回调: move_head_and_ear
2025-08-04 10:00:32.677 | INFO     | __main__:websocket_endpoint:299 - 收到消息: {"action":"move_head_and_ear","yaw_cmd":"right"}
2025-08-04 10:00:32.677 | WARNING  | src.robot_client:move_head_and_ear_direct:65 - ROS2未初始化，使用模拟模式
2025-08-04 10:00:32.677 | INFO     | src.expression_controller:handle_expression_message:83 - 表情消息处理成功: move_head_and_ear
2025-08-04 10:00:32.678 | INFO     | src.message_handler:handle_message:61 - 消息处理成功: move_head_and_ear
2025-08-04 10:00:32.679 | INFO     | __main__:enhanced_app_broadcast_callback:170 - 已转发Live2D消息: {'type': 'playMotion', 'group': 'right', 'index': 0}
2025-08-04 10:00:32.682 | INFO     | src.unified_controller:handle_websocket_message:158 - 已触发应用层广播回调: move_head_and_ear
2025-08-04 10:00:33.476 | INFO     | __main__:websocket_endpoint:299 - 收到消息: {"action":"move_head_and_ear","yaw_cmd":"center"}
2025-08-04 10:00:33.476 | WARNING  | src.robot_client:move_head_and_ear_direct:65 - ROS2未初始化，使用模拟模式
2025-08-04 10:00:33.476 | INFO     | src.expression_controller:handle_expression_message:83 - 表情消息处理成功: move_head_and_ear
2025-08-04 10:00:33.476 | INFO     | src.message_handler:handle_message:61 - 消息处理成功: move_head_and_ear
2025-08-04 10:00:33.478 | INFO     | __main__:enhanced_app_broadcast_callback:170 - 已转发Live2D消息: {'type': 'playMotion', 'group': 'center', 'index': 0}
2025-08-04 10:00:33.478 | INFO     | src.unified_controller:handle_websocket_message:158 - 已触发应用层广播回调: move_head_and_ear
2025-08-04 10:00:36.476 | INFO     | __main__:handle_live2d_client:600 - Live2D客户端 [127.0.0.1:10835] 断开连接，剩余连接数: 0
2025-08-04 10:00:36.911 | INFO     | __main__:handle_live2d_client:571 - 新的Live2D客户端连接 [127.0.0.1:10948] 在8002端口，当前连接数: 1
2025-08-04 10:00:38.506 | INFO     | __main__:websocket_endpoint:299 - 收到消息: {"action":"move_head_and_ear","yaw_cmd":"left"}
2025-08-04 10:00:38.507 | WARNING  | src.robot_client:move_head_and_ear_direct:65 - ROS2未初始化，使用模拟模式
2025-08-04 10:00:38.507 | INFO     | src.expression_controller:handle_expression_message:83 - 表情消息处理成功: move_head_and_ear
2025-08-04 10:00:38.507 | INFO     | src.message_handler:handle_message:61 - 消息处理成功: move_head_and_ear
2025-08-04 10:00:38.509 | INFO     | __main__:enhanced_app_broadcast_callback:170 - 已转发Live2D消息: {'type': 'playMotion', 'group': 'left', 'index': 0}
2025-08-04 10:00:38.509 | INFO     | src.unified_controller:handle_websocket_message:158 - 已触发应用层广播回调: move_head_and_ear
2025-08-04 10:00:39.726 | INFO     | __main__:websocket_endpoint:299 - 收到消息: {"action":"move_head_and_ear","yaw_cmd":"center"}
2025-08-04 10:00:39.726 | WARNING  | src.robot_client:move_head_and_ear_direct:65 - ROS2未初始化，使用模拟模式
2025-08-04 10:00:39.726 | INFO     | src.expression_controller:handle_expression_message:83 - 表情消息处理成功: move_head_and_ear
2025-08-04 10:00:39.727 | INFO     | src.message_handler:handle_message:61 - 消息处理成功: move_head_and_ear
2025-08-04 10:00:39.728 | INFO     | __main__:enhanced_app_broadcast_callback:170 - 已转发Live2D消息: {'type': 'playMotion', 'group': 'center', 'index': 0}
2025-08-04 10:00:39.729 | INFO     | src.unified_controller:handle_websocket_message:158 - 已触发应用层广播回调: move_head_and_ear
2025-08-04 10:00:40.700 | INFO     | __main__:websocket_endpoint:299 - 收到消息: {"action":"move_head_and_ear","yaw_cmd":"right"}
2025-08-04 10:00:40.701 | WARNING  | src.robot_client:move_head_and_ear_direct:65 - ROS2未初始化，使用模拟模式
2025-08-04 10:00:40.701 | INFO     | src.expression_controller:handle_expression_message:83 - 表情消息处理成功: move_head_and_ear
2025-08-04 10:00:40.701 | INFO     | src.message_handler:handle_message:61 - 消息处理成功: move_head_and_ear
2025-08-04 10:00:40.702 | INFO     | __main__:enhanced_app_broadcast_callback:170 - 已转发Live2D消息: {'type': 'playMotion', 'group': 'right', 'index': 0}
2025-08-04 10:00:40.702 | INFO     | src.unified_controller:handle_websocket_message:158 - 已触发应用层广播回调: move_head_and_ear
2025-08-04 10:00:40.783 | INFO     | src.action_queue_manager:clear_completed_queues:534 - 已清理 0 个已完成的队列
2025-08-04 10:00:41.839 | INFO     | __main__:websocket_endpoint:299 - 收到消息: {"action":"move_head_and_ear","yaw_cmd":"center"}
2025-08-04 10:00:41.840 | WARNING  | src.robot_client:move_head_and_ear_direct:65 - ROS2未初始化，使用模拟模式
2025-08-04 10:00:41.840 | INFO     | src.expression_controller:handle_expression_message:83 - 表情消息处理成功: move_head_and_ear
2025-08-04 10:00:41.841 | INFO     | src.message_handler:handle_message:61 - 消息处理成功: move_head_and_ear
2025-08-04 10:00:41.841 | INFO     | __main__:enhanced_app_broadcast_callback:170 - 已转发Live2D消息: {'type': 'playMotion', 'group': 'center', 'index': 0}
2025-08-04 10:00:41.842 | INFO     | src.unified_controller:handle_websocket_message:158 - 已触发应用层广播回调: move_head_and_ear
2025-08-04 10:00:42.974 | INFO     | __main__:websocket_endpoint:299 - 收到消息: {"action":"move_head_and_ear","pitch_cmd":"up"}
2025-08-04 10:00:42.975 | WARNING  | src.robot_client:move_head_and_ear_direct:65 - ROS2未初始化，使用模拟模式
2025-08-04 10:00:42.975 | INFO     | src.expression_controller:handle_expression_message:83 - 表情消息处理成功: move_head_and_ear
2025-08-04 10:00:42.976 | INFO     | src.message_handler:handle_message:61 - 消息处理成功: move_head_and_ear
2025-08-04 10:00:42.976 | INFO     | __main__:enhanced_app_broadcast_callback:170 - 已转发Live2D消息: {'type': 'playMotion', 'group': 'up', 'index': 0}
2025-08-04 10:00:42.976 | INFO     | src.unified_controller:handle_websocket_message:158 - 已触发应用层广播回调: move_head_and_ear
2025-08-04 10:00:43.750 | INFO     | __main__:websocket_endpoint:299 - 收到消息: {"action":"move_head_and_ear","pitch_cmd":"center"}
2025-08-04 10:00:43.751 | WARNING  | src.robot_client:move_head_and_ear_direct:65 - ROS2未初始化，使用模拟模式
2025-08-04 10:00:43.751 | INFO     | src.expression_controller:handle_expression_message:83 - 表情消息处理成功: move_head_and_ear
2025-08-04 10:00:43.751 | INFO     | src.message_handler:handle_message:61 - 消息处理成功: move_head_and_ear
2025-08-04 10:00:43.752 | INFO     | __main__:enhanced_app_broadcast_callback:170 - 已转发Live2D消息: {'type': 'playMotion', 'group': 'center', 'index': 0}
2025-08-04 10:00:43.752 | INFO     | src.unified_controller:handle_websocket_message:158 - 已触发应用层广播回调: move_head_and_ear
2025-08-04 10:00:44.402 | INFO     | __main__:websocket_endpoint:299 - 收到消息: {"action":"move_head_and_ear","pitch_cmd":"down"}
2025-08-04 10:00:44.403 | WARNING  | src.robot_client:move_head_and_ear_direct:65 - ROS2未初始化，使用模拟模式
2025-08-04 10:00:44.403 | INFO     | src.expression_controller:handle_expression_message:83 - 表情消息处理成功: move_head_and_ear
2025-08-04 10:00:44.403 | INFO     | src.message_handler:handle_message:61 - 消息处理成功: move_head_and_ear
2025-08-04 10:00:44.404 | INFO     | __main__:enhanced_app_broadcast_callback:170 - 已转发Live2D消息: {'type': 'playMotion', 'group': 'down', 'index': 0}
2025-08-04 10:00:44.404 | INFO     | src.unified_controller:handle_websocket_message:158 - 已触发应用层广播回调: move_head_and_ear
2025-08-04 10:00:45.201 | INFO     | __main__:websocket_endpoint:299 - 收到消息: {"action":"move_head_and_ear","pitch_cmd":"center"}
2025-08-04 10:00:45.202 | WARNING  | src.robot_client:move_head_and_ear_direct:65 - ROS2未初始化，使用模拟模式
2025-08-04 10:00:45.202 | INFO     | src.expression_controller:handle_expression_message:83 - 表情消息处理成功: move_head_and_ear
2025-08-04 10:00:45.202 | INFO     | src.message_handler:handle_message:61 - 消息处理成功: move_head_and_ear
2025-08-04 10:00:45.203 | INFO     | __main__:enhanced_app_broadcast_callback:170 - 已转发Live2D消息: {'type': 'playMotion', 'group': 'center', 'index': 0}
2025-08-04 10:00:45.203 | INFO     | src.unified_controller:handle_websocket_message:158 - 已触发应用层广播回调: move_head_and_ear
2025-08-04 10:00:45.878 | INFO     | __main__:websocket_endpoint:299 - 收到消息: {"action":"move_head_and_ear","pitch_cmd":"down"}
2025-08-04 10:00:45.879 | WARNING  | src.robot_client:move_head_and_ear_direct:65 - ROS2未初始化，使用模拟模式
2025-08-04 10:00:45.879 | INFO     | src.expression_controller:handle_expression_message:83 - 表情消息处理成功: move_head_and_ear
2025-08-04 10:00:45.879 | INFO     | src.message_handler:handle_message:61 - 消息处理成功: move_head_and_ear
2025-08-04 10:00:45.880 | INFO     | __main__:enhanced_app_broadcast_callback:170 - 已转发Live2D消息: {'type': 'playMotion', 'group': 'down', 'index': 0}
2025-08-04 10:00:45.881 | INFO     | src.unified_controller:handle_websocket_message:158 - 已触发应用层广播回调: move_head_and_ear
2025-08-04 10:00:46.601 | INFO     | __main__:websocket_endpoint:299 - 收到消息: {"action":"move_head_and_ear","pitch_cmd":"center"}
2025-08-04 10:00:46.601 | WARNING  | src.robot_client:move_head_and_ear_direct:65 - ROS2未初始化，使用模拟模式
2025-08-04 10:00:46.601 | INFO     | src.expression_controller:handle_expression_message:83 - 表情消息处理成功: move_head_and_ear
2025-08-04 10:00:46.602 | INFO     | src.message_handler:handle_message:61 - 消息处理成功: move_head_and_ear
2025-08-04 10:00:46.602 | INFO     | __main__:enhanced_app_broadcast_callback:170 - 已转发Live2D消息: {'type': 'playMotion', 'group': 'center', 'index': 0}
2025-08-04 10:00:46.603 | INFO     | src.unified_controller:handle_websocket_message:158 - 已触发应用层广播回调: move_head_and_ear
2025-08-04 10:00:47.436 | INFO     | __main__:websocket_endpoint:299 - 收到消息: {"action":"move_head_and_ear","pitch_cmd":"up"}
2025-08-04 10:00:47.437 | WARNING  | src.robot_client:move_head_and_ear_direct:65 - ROS2未初始化，使用模拟模式
2025-08-04 10:00:47.438 | INFO     | src.expression_controller:handle_expression_message:83 - 表情消息处理成功: move_head_and_ear
2025-08-04 10:00:47.441 | INFO     | src.message_handler:handle_message:61 - 消息处理成功: move_head_and_ear
2025-08-04 10:00:47.442 | INFO     | __main__:enhanced_app_broadcast_callback:170 - 已转发Live2D消息: {'type': 'playMotion', 'group': 'up', 'index': 0}
2025-08-04 10:00:47.443 | INFO     | src.unified_controller:handle_websocket_message:158 - 已触发应用层广播回调: move_head_and_ear
2025-08-04 10:00:48.068 | INFO     | __main__:websocket_endpoint:299 - 收到消息: {"action":"move_head_and_ear","pitch_cmd":"center"}
2025-08-04 10:00:48.068 | WARNING  | src.robot_client:move_head_and_ear_direct:65 - ROS2未初始化，使用模拟模式
2025-08-04 10:00:48.069 | INFO     | src.expression_controller:handle_expression_message:83 - 表情消息处理成功: move_head_and_ear
2025-08-04 10:00:48.069 | INFO     | src.message_handler:handle_message:61 - 消息处理成功: move_head_and_ear
2025-08-04 10:00:48.070 | INFO     | __main__:enhanced_app_broadcast_callback:170 - 已转发Live2D消息: {'type': 'playMotion', 'group': 'center', 'index': 0}
2025-08-04 10:00:48.070 | INFO     | src.unified_controller:handle_websocket_message:158 - 已触发应用层广播回调: move_head_and_ear
2025-08-04 10:00:48.848 | INFO     | __main__:websocket_endpoint:299 - 收到消息: {"action":"move_head_and_ear","pitch_cmd":"down"}
2025-08-04 10:00:48.848 | WARNING  | src.robot_client:move_head_and_ear_direct:65 - ROS2未初始化，使用模拟模式
2025-08-04 10:00:48.849 | INFO     | src.expression_controller:handle_expression_message:83 - 表情消息处理成功: move_head_and_ear
2025-08-04 10:00:48.849 | INFO     | src.message_handler:handle_message:61 - 消息处理成功: move_head_and_ear
2025-08-04 10:00:48.850 | INFO     | __main__:enhanced_app_broadcast_callback:170 - 已转发Live2D消息: {'type': 'playMotion', 'group': 'down', 'index': 0}
2025-08-04 10:00:48.850 | INFO     | src.unified_controller:handle_websocket_message:158 - 已触发应用层广播回调: move_head_and_ear
2025-08-04 10:00:49.377 | INFO     | __main__:websocket_endpoint:299 - 收到消息: {"action":"move_head_and_ear","pitch_cmd":"center"}
2025-08-04 10:00:49.377 | WARNING  | src.robot_client:move_head_and_ear_direct:65 - ROS2未初始化，使用模拟模式
2025-08-04 10:00:49.378 | INFO     | src.expression_controller:handle_expression_message:83 - 表情消息处理成功: move_head_and_ear
2025-08-04 10:00:49.379 | INFO     | src.message_handler:handle_message:61 - 消息处理成功: move_head_and_ear
2025-08-04 10:00:49.380 | INFO     | __main__:enhanced_app_broadcast_callback:170 - 已转发Live2D消息: {'type': 'playMotion', 'group': 'center', 'index': 0}
2025-08-04 10:00:49.380 | INFO     | src.unified_controller:handle_websocket_message:158 - 已触发应用层广播回调: move_head_and_ear
2025-08-04 10:01:40.792 | INFO     | src.action_queue_manager:clear_completed_queues:534 - 已清理 0 个已完成的队列
2025-08-04 10:02:40.791 | INFO     | src.action_queue_manager:clear_completed_queues:534 - 已清理 0 个已完成的队列
2025-08-04 10:03:40.812 | INFO     | src.action_queue_manager:clear_completed_queues:534 - 已清理 0 个已完成的队列
2025-08-04 10:04:40.815 | INFO     | src.action_queue_manager:clear_completed_queues:534 - 已清理 0 个已完成的队列
2025-08-04 10:05:40.821 | INFO     | src.action_queue_manager:clear_completed_queues:534 - 已清理 0 个已完成的队列
2025-08-04 10:06:17.794 | INFO     | __main__:handle_live2d_client:600 - Live2D客户端 [127.0.0.1:10948] 断开连接，剩余连接数: 0
2025-08-04 10:06:18.357 | INFO     | __main__:handle_live2d_client:571 - 新的Live2D客户端连接 [127.0.0.1:12062] 在8002端口，当前连接数: 1
2025-08-04 10:06:39.259 | INFO     | __main__:handle_live2d_client:600 - Live2D客户端 [127.0.0.1:12062] 断开连接，剩余连接数: 0
2025-08-04 10:06:39.757 | INFO     | __main__:handle_live2d_client:571 - 新的Live2D客户端连接 [127.0.0.1:12164] 在8002端口，当前连接数: 1
2025-08-04 10:06:40.835 | INFO     | src.action_queue_manager:clear_completed_queues:534 - 已清理 0 个已完成的队列
2025-08-04 10:07:02.888 | INFO     | __main__:handle_live2d_client:600 - Live2D客户端 [127.0.0.1:12164] 断开连接，剩余连接数: 0
2025-08-04 10:07:03.346 | INFO     | __main__:handle_live2d_client:571 - 新的Live2D客户端连接 [127.0.0.1:12228] 在8002端口，当前连接数: 1
2025-08-04 10:07:26.522 | INFO     | __main__:handle_live2d_client:600 - Live2D客户端 [127.0.0.1:12228] 断开连接，剩余连接数: 0
2025-08-04 10:07:27.009 | INFO     | __main__:handle_live2d_client:571 - 新的Live2D客户端连接 [127.0.0.1:12382] 在8002端口，当前连接数: 1
2025-08-04 10:07:40.852 | INFO     | src.action_queue_manager:clear_completed_queues:534 - 已清理 0 个已完成的队列
2025-08-04 10:07:40.953 | INFO     | __main__:handle_live2d_client:600 - Live2D客户端 [127.0.0.1:12382] 断开连接，剩余连接数: 0
2025-08-04 10:07:41.413 | INFO     | __main__:handle_live2d_client:571 - 新的Live2D客户端连接 [127.0.0.1:12427] 在8002端口，当前连接数: 1
2025-08-04 10:08:17.627 | INFO     | __main__:handle_live2d_client:600 - Live2D客户端 [127.0.0.1:12427] 断开连接，剩余连接数: 0
2025-08-04 10:08:18.062 | INFO     | __main__:handle_live2d_client:571 - 新的Live2D客户端连接 [127.0.0.1:12528] 在8002端口，当前连接数: 1
2025-08-04 10:08:40.869 | INFO     | src.action_queue_manager:clear_completed_queues:534 - 已清理 0 个已完成的队列
2025-08-04 10:09:05.305 | INFO     | __main__:handle_live2d_client:600 - Live2D客户端 [127.0.0.1:12528] 断开连接，剩余连接数: 0
2025-08-04 10:09:05.770 | INFO     | __main__:handle_live2d_client:571 - 新的Live2D客户端连接 [127.0.0.1:12647] 在8002端口，当前连接数: 1
2025-08-04 10:09:40.887 | INFO     | src.action_queue_manager:clear_completed_queues:534 - 已清理 0 个已完成的队列
2025-08-04 10:10:40.906 | INFO     | src.action_queue_manager:clear_completed_queues:534 - 已清理 0 个已完成的队列
2025-08-04 10:11:40.903 | INFO     | src.action_queue_manager:clear_completed_queues:534 - 已清理 0 个已完成的队列
2025-08-04 10:12:40.899 | INFO     | src.action_queue_manager:clear_completed_queues:534 - 已清理 0 个已完成的队列
2025-08-04 10:13:40.897 | INFO     | src.action_queue_manager:clear_completed_queues:534 - 已清理 0 个已完成的队列
2025-08-04 10:14:40.923 | INFO     | src.action_queue_manager:clear_completed_queues:534 - 已清理 0 个已完成的队列
2025-08-04 10:15:40.914 | INFO     | src.action_queue_manager:clear_completed_queues:534 - 已清理 0 个已完成的队列
2025-08-04 10:16:37.464 | INFO     | __main__:websocket_endpoint:299 - 收到消息: {"action":"move_head_and_ear","yaw_cmd":"left"}
2025-08-04 10:16:37.464 | WARNING  | src.robot_client:move_head_and_ear_direct:65 - ROS2未初始化，使用模拟模式
2025-08-04 10:16:37.465 | INFO     | src.expression_controller:handle_expression_message:83 - 表情消息处理成功: move_head_and_ear
2025-08-04 10:16:37.465 | INFO     | src.message_handler:handle_message:61 - 消息处理成功: move_head_and_ear
2025-08-04 10:16:37.466 | INFO     | __main__:enhanced_app_broadcast_callback:170 - 已转发Live2D消息: {'type': 'playMotion', 'group': 'left', 'index': 0}
2025-08-04 10:16:37.467 | INFO     | src.unified_controller:handle_websocket_message:158 - 已触发应用层广播回调: move_head_and_ear
2025-08-04 10:16:39.601 | INFO     | __main__:websocket_endpoint:299 - 收到消息: {"action":"move_head_and_ear","yaw_cmd":"right"}
2025-08-04 10:16:39.602 | WARNING  | src.robot_client:move_head_and_ear_direct:65 - ROS2未初始化，使用模拟模式
2025-08-04 10:16:39.603 | INFO     | src.expression_controller:handle_expression_message:83 - 表情消息处理成功: move_head_and_ear
2025-08-04 10:16:39.604 | INFO     | src.message_handler:handle_message:61 - 消息处理成功: move_head_and_ear
2025-08-04 10:16:39.605 | INFO     | __main__:enhanced_app_broadcast_callback:170 - 已转发Live2D消息: {'type': 'playMotion', 'group': 'right', 'index': 0}
2025-08-04 10:16:39.606 | INFO     | src.unified_controller:handle_websocket_message:158 - 已触发应用层广播回调: move_head_and_ear
2025-08-04 10:16:40.936 | INFO     | src.action_queue_manager:clear_completed_queues:534 - 已清理 0 个已完成的队列
2025-08-04 10:16:41.038 | INFO     | __main__:websocket_endpoint:299 - 收到消息: {"action":"move_head_and_ear","yaw_cmd":"center"}
2025-08-04 10:16:41.039 | WARNING  | src.robot_client:move_head_and_ear_direct:65 - ROS2未初始化，使用模拟模式
2025-08-04 10:16:41.039 | INFO     | src.expression_controller:handle_expression_message:83 - 表情消息处理成功: move_head_and_ear
2025-08-04 10:16:41.040 | INFO     | src.message_handler:handle_message:61 - 消息处理成功: move_head_and_ear
2025-08-04 10:16:41.041 | INFO     | __main__:enhanced_app_broadcast_callback:170 - 已转发Live2D消息: {'type': 'playMotion', 'group': 'center', 'index': 0}
2025-08-04 10:16:41.041 | INFO     | src.unified_controller:handle_websocket_message:158 - 已触发应用层广播回调: move_head_and_ear
2025-08-04 10:16:42.434 | INFO     | __main__:websocket_endpoint:299 - 收到消息: {"action":"move_head_and_ear","yaw_cmd":"right"}
2025-08-04 10:16:42.434 | WARNING  | src.robot_client:move_head_and_ear_direct:65 - ROS2未初始化，使用模拟模式
2025-08-04 10:16:42.435 | INFO     | src.expression_controller:handle_expression_message:83 - 表情消息处理成功: move_head_and_ear
2025-08-04 10:16:42.435 | INFO     | src.message_handler:handle_message:61 - 消息处理成功: move_head_and_ear
2025-08-04 10:16:42.436 | INFO     | __main__:enhanced_app_broadcast_callback:170 - 已转发Live2D消息: {'type': 'playMotion', 'group': 'right', 'index': 0}
2025-08-04 10:16:42.437 | INFO     | src.unified_controller:handle_websocket_message:158 - 已触发应用层广播回调: move_head_and_ear
2025-08-04 10:16:44.428 | INFO     | __main__:websocket_endpoint:299 - 收到消息: {"action":"move_head_and_ear","yaw_cmd":"center"}
2025-08-04 10:16:44.429 | WARNING  | src.robot_client:move_head_and_ear_direct:65 - ROS2未初始化，使用模拟模式
2025-08-04 10:16:44.429 | INFO     | src.expression_controller:handle_expression_message:83 - 表情消息处理成功: move_head_and_ear
2025-08-04 10:16:44.430 | INFO     | src.message_handler:handle_message:61 - 消息处理成功: move_head_and_ear
2025-08-04 10:16:44.431 | INFO     | __main__:enhanced_app_broadcast_callback:170 - 已转发Live2D消息: {'type': 'playMotion', 'group': 'center', 'index': 0}
2025-08-04 10:16:44.431 | INFO     | src.unified_controller:handle_websocket_message:158 - 已触发应用层广播回调: move_head_and_ear
2025-08-04 10:16:45.374 | INFO     | __main__:websocket_endpoint:299 - 收到消息: {"action":"move_head_and_ear","yaw_cmd":"left"}
2025-08-04 10:16:45.375 | WARNING  | src.robot_client:move_head_and_ear_direct:65 - ROS2未初始化，使用模拟模式
2025-08-04 10:16:45.375 | INFO     | src.expression_controller:handle_expression_message:83 - 表情消息处理成功: move_head_and_ear
2025-08-04 10:16:45.376 | INFO     | src.message_handler:handle_message:61 - 消息处理成功: move_head_and_ear
2025-08-04 10:16:45.378 | INFO     | __main__:enhanced_app_broadcast_callback:170 - 已转发Live2D消息: {'type': 'playMotion', 'group': 'left', 'index': 0}
2025-08-04 10:16:45.378 | INFO     | src.unified_controller:handle_websocket_message:158 - 已触发应用层广播回调: move_head_and_ear
2025-08-04 10:16:46.696 | INFO     | __main__:websocket_endpoint:299 - 收到消息: {"action":"move_head_and_ear","yaw_cmd":"left"}
2025-08-04 10:16:46.696 | WARNING  | src.robot_client:move_head_and_ear_direct:65 - ROS2未初始化，使用模拟模式
2025-08-04 10:16:46.697 | INFO     | src.expression_controller:handle_expression_message:83 - 表情消息处理成功: move_head_and_ear
2025-08-04 10:16:46.697 | INFO     | src.message_handler:handle_message:61 - 消息处理成功: move_head_and_ear
2025-08-04 10:16:46.698 | INFO     | __main__:enhanced_app_broadcast_callback:170 - 已转发Live2D消息: {'type': 'playMotion', 'group': 'left', 'index': 0}
2025-08-04 10:16:46.698 | INFO     | src.unified_controller:handle_websocket_message:158 - 已触发应用层广播回调: move_head_and_ear
2025-08-04 10:16:47.794 | INFO     | __main__:websocket_endpoint:299 - 收到消息: {"action":"move_head_and_ear","yaw_cmd":"right"}
2025-08-04 10:16:47.795 | WARNING  | src.robot_client:move_head_and_ear_direct:65 - ROS2未初始化，使用模拟模式
2025-08-04 10:16:47.795 | INFO     | src.expression_controller:handle_expression_message:83 - 表情消息处理成功: move_head_and_ear
2025-08-04 10:16:47.796 | INFO     | src.message_handler:handle_message:61 - 消息处理成功: move_head_and_ear
2025-08-04 10:16:47.797 | INFO     | __main__:enhanced_app_broadcast_callback:170 - 已转发Live2D消息: {'type': 'playMotion', 'group': 'right', 'index': 0}
2025-08-04 10:16:47.797 | INFO     | src.unified_controller:handle_websocket_message:158 - 已触发应用层广播回调: move_head_and_ear
2025-08-04 10:16:49.991 | INFO     | src.connection_manager:disconnect:27 - 连接已断开，当前连接数: 0
2025-08-04 10:16:49.991 | INFO     | __main__:websocket_endpoint:335 - WebSocket连接断开: Address(host='127.0.0.1', port=10942)
2025-08-04 10:17:40.913 | INFO     | src.action_queue_manager:clear_completed_queues:534 - 已清理 0 个已完成的队列
2025-08-04 10:18:40.916 | INFO     | src.action_queue_manager:clear_completed_queues:534 - 已清理 0 个已完成的队列
2025-08-04 10:19:40.931 | INFO     | src.action_queue_manager:clear_completed_queues:534 - 已清理 0 个已完成的队列
2025-08-04 10:20:30.472 | INFO     | __main__:handle_live2d_client:600 - Live2D客户端 [127.0.0.1:12647] 断开连接，剩余连接数: 0
2025-08-04 10:20:31.083 | INFO     | __main__:handle_live2d_client:571 - 新的Live2D客户端连接 [127.0.0.1:13838] 在8002端口，当前连接数: 1
